#!/usr/bin/env python3
"""
测试部署脚本 - 验证大语言模型管理系统是否正常运行
"""

import requests
import sys
import time
from urllib.parse import urljoin

def test_application():
    """测试应用程序是否正常运行"""
    base_url = "http://localhost:5000"
    
    print("🚀 开始测试大语言模型管理系统...")
    
    # 测试1: 检查主页重定向
    print("\n📋 测试1: 检查主页重定向...")
    try:
        response = requests.get(base_url, allow_redirects=False, timeout=10)
        if response.status_code == 302:
            print("✅ 主页重定向正常")
        else:
            print(f"❌ 主页重定向失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 主页访问失败: {e}")
        return False
    
    # 测试2: 检查登录页面
    print("\n📋 测试2: 检查登录页面...")
    try:
        login_url = urljoin(base_url, "/auth/login")
        response = requests.get(login_url, timeout=10)
        if response.status_code == 200:
            print("✅ 登录页面访问正常")
            if "大语言模型管理系统" in response.text:
                print("✅ 登录页面内容正确")
            else:
                print("❌ 登录页面内容不正确")
                return False
        else:
            print(f"❌ 登录页面访问失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录页面访问失败: {e}")
        return False
    
    # 测试3: 检查数据库连接
    print("\n📋 测试3: 检查数据库连接...")
    try:
        # 通过访问需要数据库的页面来测试数据库连接
        response = requests.get(login_url, timeout=10)
        if response.status_code == 200:
            print("✅ 数据库连接正常")
        else:
            print("❌ 数据库连接可能有问题")
            return False
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False
    
    # 测试4: 检查静态文件
    print("\n📋 测试4: 检查静态文件...")
    try:
        # 检查Bootstrap CSS是否可以访问
        if "bootstrap" in response.text.lower():
            print("✅ 静态文件加载正常")
        else:
            print("⚠️  静态文件可能有问题，但不影响基本功能")
    except Exception as e:
        print(f"⚠️  静态文件检查失败: {e}")
    
    print("\n🎉 所有核心功能测试通过！")
    print("\n📝 系统信息:")
    print(f"   - 应用地址: {base_url}")
    print(f"   - 登录地址: {login_url}")
    print(f"   - 数据库: MySQL (端口 3307)")
    print(f"   - 环境: 开发环境")
    
    print("\n🔧 下一步操作:")
    print("   1. 访问 http://localhost:5000 开始使用系统")
    print("   2. 使用管理员密码登录系统")
    print("   3. 添加AI平台和模型")
    print("   4. 创建应用并配置模型")
    
    return True

def check_containers():
    """检查Docker容器状态"""
    print("\n🐳 检查Docker容器状态...")
    import subprocess
    
    try:
        result = subprocess.run(
            ["docker", "compose", "-f", "docker-compose.dev.yml", "ps"],
            capture_output=True,
            text=True,
            cwd="."
        )
        
        if result.returncode == 0:
            print("✅ Docker容器状态:")
            print(result.stdout)
        else:
            print("❌ Docker容器检查失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Docker容器检查失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("🔍 大语言模型管理系统 - 部署测试")
    print("=" * 60)
    
    # 等待服务启动
    print("\n⏳ 等待服务启动...")
    time.sleep(3)
    
    # 检查容器状态
    if not check_containers():
        sys.exit(1)
    
    # 测试应用
    if test_application():
        print("\n" + "=" * 60)
        print("🎊 部署测试成功！系统已准备就绪！")
        print("=" * 60)
        sys.exit(0)
    else:
        print("\n" + "=" * 60)
        print("💥 部署测试失败！请检查日志。")
        print("=" * 60)
        sys.exit(1)
