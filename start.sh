#!/bin/bash

# MariaDB Model Manager - Docker Compose V2 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 检查 Docker 和 Docker Compose
check_requirements() {
    print_message "检查系统要求..." $BLUE
    
    if ! command -v docker &> /dev/null; then
        print_message "错误: Docker 未安装" $RED
        exit 1
    fi
    
    if ! docker compose version &> /dev/null; then
        print_message "错误: Docker Compose V2 未安装" $RED
        print_message "请安装 Docker Compose V2 或使用 'docker-compose' 命令" $YELLOW
        exit 1
    fi
    
    print_message "✓ Docker 和 Docker Compose V2 已安装" $GREEN
}

# 选择环境
select_environment() {
    echo
    print_message "请选择部署环境:" $BLUE
    echo "1) 生产环境 (Production)"
    echo "2) 开发环境 (Development)"
    echo "3) 退出"
    
    read -p "请输入选择 (1-3): " choice
    
    case $choice in
        1)
            ENVIRONMENT="production"
            COMPOSE_FILE="docker-compose.yml"
            ENV_FILE=".env.production"
            ;;
        2)
            ENVIRONMENT="development"
            COMPOSE_FILE="docker-compose.dev.yml"
            ENV_FILE=".env.development"
            ;;
        3)
            print_message "退出安装" $YELLOW
            exit 0
            ;;
        *)
            print_message "无效选择，请重新运行脚本" $RED
            exit 1
            ;;
    esac
}

# 准备环境配置
prepare_environment() {
    print_message "准备 $ENVIRONMENT 环境配置..." $BLUE
    
    if [ ! -f "$ENV_FILE" ]; then
        print_message "创建环境配置文件: $ENV_FILE" $YELLOW
        cp .env.example "$ENV_FILE"
        
        if [ "$ENVIRONMENT" = "production" ]; then
            print_message "⚠️  重要: 请编辑 $ENV_FILE 文件，修改以下配置:" $YELLOW
            echo "  - SECRET_KEY (必须更改)"
            echo "  - ADMIN_PASSWORD (必须更改)"
            echo "  - DB_ROOT_PASSWORD (必须更改)"
            echo "  - DB_PASSWORD (必须更改)"
            echo "  - 各平台的 API 密钥"
            echo
            read -p "是否现在编辑配置文件? (y/n): " edit_config
            
            if [ "$edit_config" = "y" ] || [ "$edit_config" = "Y" ]; then
                ${EDITOR:-nano} "$ENV_FILE"
            fi
        fi
    else
        print_message "✓ 环境配置文件已存在: $ENV_FILE" $GREEN
    fi
}

# 构建和启动服务
start_services() {
    print_message "构建和启动 $ENVIRONMENT 环境服务..." $BLUE
    
    if [ "$ENVIRONMENT" = "development" ]; then
        docker compose -f "$COMPOSE_FILE" build
        docker compose -f "$COMPOSE_FILE" up -d
    else
        docker compose build
        docker compose up -d
    fi
    
    print_message "等待服务启动..." $YELLOW
    sleep 10
}

# 检查服务状态
check_services() {
    print_message "检查服务状态..." $BLUE
    
    if [ "$ENVIRONMENT" = "development" ]; then
        docker compose -f "$COMPOSE_FILE" ps
    else
        docker compose ps
    fi
    
    # 检查应用健康状态
    print_message "检查应用健康状态..." $BLUE
    
    for i in {1..30}; do
        if curl -f http://localhost:5000/api/v1/health &> /dev/null; then
            print_message "✓ 应用启动成功!" $GREEN
            break
        fi
        
        if [ $i -eq 30 ]; then
            print_message "⚠️  应用可能启动失败，请检查日志" $YELLOW
        else
            echo -n "."
            sleep 2
        fi
    done
}

# 显示访问信息
show_access_info() {
    echo
    print_message "🎉 部署完成!" $GREEN
    echo
    print_message "访问信息:" $BLUE
    echo "  Web 界面: http://localhost:5000"
    echo "  API 健康检查: http://localhost:5000/api/v1/health"
    
    if [ "$ENVIRONMENT" = "development" ]; then
        echo "  数据库端口: 3307"
    else
        echo "  数据库端口: 3306"
    fi
    
    echo
    print_message "常用命令:" $BLUE
    echo "  查看日志: make logs (或 make dev-logs)"
    echo "  停止服务: make down (或 make dev-down)"
    echo "  重启服务: make restart"
    echo "  数据库连接: make db-shell (或 make dev-db-shell)"
    echo "  查看帮助: make help"
    
    echo
    print_message "配置文件位置: $ENV_FILE" $YELLOW
}

# 主函数
main() {
    print_message "MariaDB Model Manager - Docker Compose V2 部署" $BLUE
    print_message "================================================" $BLUE
    
    check_requirements
    select_environment
    prepare_environment
    start_services
    check_services
    show_access_info
}

# 运行主函数
main "$@"
