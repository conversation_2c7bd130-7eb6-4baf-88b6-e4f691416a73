FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY app/requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 安装开发工具
RUN pip install --no-cache-dir \
    flask-shell-ipython \
    ipython \
    pytest \
    pytest-flask \
    pytest-cov

# 注意：开发环境不复制代码，而是通过volume挂载

# 创建必要的目录
RUN mkdir -p logs uploads

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=app.py
ENV FLASK_ENV=development

# 暴露端口
EXPOSE 5000

# 开发环境启动命令（支持热重载）
CMD ["python", "-m", "flask", "run", "--host=0.0.0.0", "--port=5000", "--reload", "--debugger"]
