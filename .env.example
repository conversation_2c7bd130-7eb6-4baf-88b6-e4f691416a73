# Flask 环境配置
FLASK_ENV=development
FLASK_DEBUG=True

# 安全配置
SECRET_KEY=your-secret-key-here-change-in-production

# 数据库配置
# 生产环境数据库连接（从docker-compose.yml中提取）
DATABASE_URL=mysql+pymysql://root:rw80827@*************/model_registry
# 开发环境数据库连接
# DATABASE_URL=mysql+pymysql://root:password@localhost/model_registry_dev

# 管理员配置
# 从docker-compose.yml中提取的默认密码
ADMIN_PASSWORD=your_secure_password

# API 配置
API_ENABLED=true
API_VERSION=v1

# 安全设置
MAX_LOGIN_ATTEMPTS=5
LOGIN_ATTEMPT_TIMEOUT=300

# 分页配置
ITEMS_PER_PAGE=20

# 日志配置
LOG_LEVEL=INFO

# 会话配置
SESSION_COOKIE_SECURE=false
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Lax

# 数据库连接池配置
DB_POOL_SIZE=10
DB_POOL_TIMEOUT=20
DB_POOL_RECYCLE=-1
DB_POOL_PRE_PING=true

# 速率限制配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_DEFAULT=100
RATE_LIMIT_WINDOW=3600

# 文件上传配置
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=uploads

# 缓存配置
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300

# 邮件配置（如果需要）
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password

# 监控配置
MONITORING_ENABLED=false
METRICS_ENDPOINT=/metrics

# 备份配置
BACKUP_ENABLED=false
BACKUP_INTERVAL=86400  # 24小时
BACKUP_RETENTION=7     # 保留7天

# 预设平台配置（从init_db.py中提取）
# 这些配置用于初始化数据库时创建默认平台
INIT_PLATFORMS_ENABLED=true

# Spark平台配置
SPARK_PLATFORM_NAME=spark
SPARK_BASE_URL=https://spark-api-open.xf-yun.com/v1
SPARK_API_KEY=fb4d51d6a4e8802f6c0febf24ac75d58:YmNhZWY2YmI0MDU0ZTU2YjRjMTdhMGU1

# OpenRouter平台配置
OPENROUTER_PLATFORM_NAME=OpenRouter
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_API_KEY=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

# DeepBricks平台配置
DEEPBRICKS_PLATFORM_NAME=deepbricks
DEEPBRICKS_BASE_URL=https://api.deepbricks.ai/v1/
DEEPBRICKS_API_KEY=sk-pmLx4UQDPWYhQYJqc8MQgbQ6pkmkRoQ6YxTumIy5nQWjSnIR

# 应用运行配置
FLASK_RUN_HOST=0.0.0.0
FLASK_RUN_PORT=5000
