-- MySQL dump 10.13  Distrib 8.0.42, for Linux (x86_64)
--
-- Host: *************    Database: model_registry
-- ------------------------------------------------------
-- Server version	5.5.5-10.6.21-MariaDB-0ubuntu0.22.04.2

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `ai_models`
--

DROP TABLE IF EXISTS `ai_models`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_models` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `display_name` varchar(50) NOT NULL,
  `internal_name` varchar(100) NOT NULL,
  `api_endpoint` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `input_token_price` decimal(10,6) DEFAULT 0.000000,
  `output_token_price` decimal(10,6) DEFAULT 0.000000,
  `input_picture_price` decimal(10,6) DEFAULT 0.000000,
  `is_visible_model` tinyint(1) DEFAULT 0,
  `free` tinyint(1) DEFAULT 0,
  `high_price` tinyint(1) DEFAULT 0,
  `platform_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `display_name` (`display_name`),
  KEY `platform_id` (`platform_id`),
  CONSTRAINT `ai_models_ibfk_1` FOREIGN KEY (`platform_id`) REFERENCES `platforms` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ai_models`
--

LOCK TABLES `ai_models` WRITE;
/*!40000 ALTER TABLE `ai_models` DISABLE KEYS */;
INSERT INTO `ai_models` VALUES (1,'deepseek-V3:free','deepseek/deepseek-chat-v3-0324:free','https://openrouter.ai/api/v1','2025-05-01 10:41:04',0.000000,0.000000,0.000000,0,1,0,4),(2,'deepseek-V3:aliyun','deepseek-v3','https://dashscope.aliyuncs.com/compatible-mode/v1','2025-05-01 10:42:13',0.000270,0.001100,0.000000,0,0,0,1),(3,'deepseek-V3:deepseek','deepseek-chat','https://api.deepseek.com','2025-05-01 10:43:13',0.000270,0.001100,0.000000,0,0,0,2),(4,'deepseek-r1:free','deepseek/deepseek-r1:free','https://openrouter.ai/api/v1','2025-05-01 10:43:49',0.000000,0.000000,0.000000,0,1,0,4),(5,'deepseek-r1:aliyun','deepseek-r1','https://dashscope.aliyuncs.com/compatible-mode/v1','2025-05-01 10:44:52',0.000550,0.002200,0.000000,0,0,0,1),(6,'deepseek-r1:deepseek','deepseek-reasoner','https://api.deepseek.com','2025-05-01 10:45:37',0.000550,0.002200,0.000000,0,0,0,2),(7,'GLM-4-Plus:zhipu','glm-4-plus','https://open.bigmodel.cn/api/paas/v4/','2025-05-01 10:46:38',0.007000,0.007000,0.000000,0,0,1,3),(8,'GLM-4V-Plus-0111:zhipu','GLM-4V-Plus-0111','https://open.bigmodel.cn/api/paas/v4/','2025-05-01 10:47:31',0.007000,0.007000,0.000000,1,0,0,3),(9,'O3-mini','o3-mini','https://api.deepbricks.ai/v1/','2025-05-01 10:48:43',0.001200,0.005000,0.000000,0,0,1,5),(10,'GPT-4o-2024-08-06','gpt-4o-2024-08-06','https://api.deepbricks.ai/v1/','2025-05-01 10:49:42',0.001000,0.004000,0.000000,1,0,1,5),(11,'GPT-4o-mini','gpt-4o-mini','https://api.deepbricks.ai/v1/','2025-05-01 10:50:36',0.000125,0.000500,0.000000,0,0,0,5),(12,'claude-sonnet-4','anthropic/claude-sonnet-4','https://openrouter.ai/api/v1','2025-05-01 10:51:37',0.003000,0.015000,0.004800,1,0,1,4),(13,'claude-3.5-sonnet','claude-3.5-sonnet','https://api.deepbricks.ai/v1/','2025-05-01 10:52:54',0.002000,0.010000,0.004800,1,0,1,5),(14,'claude-3.5-Haiku','anthropic/claude-3.5-haiku','https://openrouter.ai/api/v1','2025-05-01 10:53:55',0.000800,0.004000,0.000000,0,0,0,4),(16,'Gemini Pro 2.5 Experimental (free)','google/gemini-2.5-pro-exp-03-25','https://openrouter.ai/api/v1','2025-05-01 10:56:52',0.000000,0.000000,0.000000,1,1,0,4),(17,'Gemini Flash 2.0 thinking Experiment','google/gemini-2.0-flash-thinking-exp:free','https://openrouter.ai/api/v1','2025-05-01 10:57:37',0.000000,0.000000,0.000000,0,1,0,4),(18,'Gemini 2.5 Pro','google/gemini-2.5-pro','https://openrouter.ai/api/v1','2025-05-02 14:10:05',0.002500,0.015000,0.005160,1,0,1,4),(19,'gemini-2.5-flash-preview','google/gemini-2.5-flash-preview','https://openrouter.ai/api/v1','2025-05-02 14:13:40',0.000150,0.000600,0.000619,1,0,0,4),(20,'Gemini 2.5 Flash Preview (thinking)','google/gemini-2.5-flash-preview:thinking','https://openrouter.ai/api/v1','2025-05-02 14:19:16',0.000150,0.003500,0.000619,1,0,1,4),(21,'Qwen3 235B A22B (free)','qwen/qwen3-235b-a22b:free','https://openrouter.ai/api/v1','2025-05-02 14:21:30',0.000000,0.000000,0.000000,0,1,0,4),(22,'Qwen3 235B A22B','qwen/qwen3-235b-a22b','https://openrouter.ai/api/v1','2025-05-02 14:23:09',0.000100,0.000100,0.000000,0,0,0,4),(23,'Qwen2.5 VL 72B Instruct (free)','qwen/qwen2.5-vl-72b-instruct:free','https://openrouter.ai/api/v1','2025-05-02 14:24:13',0.000000,0.000000,0.000000,1,1,0,4),(24,'Qwen VL Plus','qwen/qwen-vl-plus','https://openrouter.ai/api/v1','2025-05-02 14:26:27',0.000210,0.000628,0.000000,1,0,0,4),(25,'qwen VL Max Latest','qwen-vl-max-latest','https://dashscope.aliyuncs.com/compatible-mode/v1','2025-05-02 14:58:22',0.000500,0.001200,0.000000,1,0,0,1),(26,'Claude 3.7 Sonnet (thinking)','anthropic/claude-3.7-sonnet:thinking','https://openrouter.ai/api/v1','2025-05-07 04:12:04',0.003000,0.015000,0.000000,1,0,1,4);
/*!40000 ALTER TABLE `ai_models` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_models`
--

DROP TABLE IF EXISTS `app_models`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_models` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `application_id` int(11) NOT NULL,
  `model_id` int(11) NOT NULL,
  `is_default` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uix_app_model` (`application_id`,`model_id`),
  KEY `idx_app_model_model` (`model_id`),
  KEY `ix_app_models_created_at` (`created_at`),
  KEY `ix_app_models_application_id` (`application_id`),
  KEY `idx_app_model_app` (`application_id`),
  KEY `idx_app_model_default` (`is_default`),
  KEY `ix_app_models_model_id` (`model_id`),
  KEY `ix_app_models_is_default` (`is_default`),
  CONSTRAINT `app_models_ibfk_1` FOREIGN KEY (`application_id`) REFERENCES `applications` (`id`),
  CONSTRAINT `app_models_ibfk_2` FOREIGN KEY (`model_id`) REFERENCES `ai_models` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_models`
--

LOCK TABLES `app_models` WRITE;
/*!40000 ALTER TABLE `app_models` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_models` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `application_models`
--

DROP TABLE IF EXISTS `application_models`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `application_models` (
  `application_id` int(11) NOT NULL,
  `model_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`application_id`,`model_id`),
  KEY `model_id` (`model_id`),
  CONSTRAINT `application_models_ibfk_1` FOREIGN KEY (`application_id`) REFERENCES `applications` (`id`),
  CONSTRAINT `application_models_ibfk_2` FOREIGN KEY (`model_id`) REFERENCES `ai_models` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `application_models`
--

LOCK TABLES `application_models` WRITE;
/*!40000 ALTER TABLE `application_models` DISABLE KEYS */;
/*!40000 ALTER TABLE `application_models` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `applications`
--

DROP TABLE IF EXISTS `applications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `applications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `api_key` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `api_key` (`api_key`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `applications`
--

LOCK TABLES `applications` WRITE;
/*!40000 ALTER TABLE `applications` DISABLE KEYS */;
INSERT INTO `applications` VALUES (1,'chat_system','聊天系统','2025-05-01 11:14:34','a2f86360098c825b2411cc57bacee801'),(2,'Physics_experiments','学生物理实验验证助手','2025-05-01 13:40:10','08b6831496a5224d6d750a8d959ca56e'),(3,'code_generator','','2025-05-07 06:31:33','1e8a2b1e4639e2451c134cf7c4c2c425');
/*!40000 ALTER TABLE `applications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `platforms`
--

DROP TABLE IF EXISTS `platforms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `platforms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `base_url` varchar(255) NOT NULL,
  `api_key` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `platforms`
--

LOCK TABLES `platforms` WRITE;
/*!40000 ALTER TABLE `platforms` DISABLE KEYS */;
INSERT INTO `platforms` VALUES (1,'dashscope','https://dashscope.aliyuncs.com/compatible-mode/v1','sk-d4a1d2d141b049129a9a0ba97165c6df','2025-05-01 10:34:15'),(2,'deepseek','https://api.deepseek.com','***********************************','2025-05-01 10:34:59'),(3,'GLM','https://open.bigmodel.cn/api/paas/v4/','5f75aff7cf3529f24bc174f14e505c60.CjP9A7jx4K7pIxdq','2025-05-01 10:35:35'),(4,'OpenRouter','https://openrouter.ai/api/v1','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','2025-05-01 10:36:20'),(5,'deepbricks','https://api.deepbricks.ai/v1/','sk-pmLx4UQDPWYhQYJqc8MQgbQ6pkmkRoQ6YxTumIy5nQWjSnIR','2025-05-01 10:36:55');
/*!40000 ALTER TABLE `platforms` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'model_registry'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-12 12:30:55
