# 配置管理说明

本项目支持多环境配置管理，通过环境变量文件来管理不同环境的配置。

## 环境配置文件

### 文件结构
```
.env.example          # 配置模板文件
.env.development      # 开发环境配置
.env.production       # 生产环境配置
.env.testing          # 测试环境配置（可选）
```

### 配置文件优先级
1. 特定环境文件（如 `.env.production`）
2. 通用 `.env` 文件
3. 系统环境变量

## 从原项目迁移的配置

### 数据库配置
从 `docker-compose.yml` 中提取的原始配置：
```
DATABASE_URL=mysql+pymysql://root:rw80827@114.96.68.254/model_registry
```

### 管理员密码
从 `docker-compose.yml` 中提取：
```
ADMIN_PASSWORD=your_secure_password
```

### 预设平台配置
从 `app/init_db.py` 中提取的平台信息：

1. **Spark 平台**
   - 名称: spark
   - URL: https://spark-api-open.xf-yun.com/v1
   - API密钥: fb4d51d6a4e8802f6c0febf24ac75d58:YmNhZWY2YmI0MDU0ZTU2YjRjMTdhMGU1

2. **OpenRouter 平台**
   - 名称: OpenRouter
   - URL: https://openrouter.ai/api/v1
   - API密钥: 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

3. **DeepBricks 平台**
   - 名称: deepbricks
   - URL: https://api.deepbricks.ai/v1/
   - API密钥: sk-pmLx4UQDPWYhQYJqc8MQgbQ6pkmkRoQ6YxTumIy5nQWjSnIR

## 使用配置管理工具

### 安装依赖
```bash
pip install python-dotenv
```

### 创建配置文件
```bash
# 创建开发环境配置
python scripts/config_manager.py create development

# 创建生产环境配置
python scripts/config_manager.py create production

# 创建测试环境配置
python scripts/config_manager.py create testing
```

### 验证配置文件
```bash
python scripts/config_manager.py validate .env.production
```

### 列出所有配置文件
```bash
python scripts/config_manager.py list
```

### 生成安全密钥
```bash
python scripts/config_manager.py generate-key
```

## 环境变量说明

### 核心配置
- `FLASK_ENV`: Flask 环境 (development/production/testing)
- `FLASK_DEBUG`: 调试模式 (True/False)
- `SECRET_KEY`: Flask 密钥（生产环境必须更改）
- `DATABASE_URL`: 数据库连接字符串

### 安全配置
- `ADMIN_PASSWORD`: 管理员密码
- `MAX_LOGIN_ATTEMPTS`: 最大登录尝试次数
- `LOGIN_ATTEMPT_TIMEOUT`: 登录尝试超时时间（秒）
- `SESSION_COOKIE_SECURE`: 会话Cookie安全标志

### API配置
- `API_ENABLED`: 是否启用API功能
- `API_VERSION`: API版本
- `RATE_LIMIT_ENABLED`: 是否启用速率限制

### 数据库配置
- `DB_POOL_SIZE`: 连接池大小
- `DB_POOL_TIMEOUT`: 连接超时时间
- `SQLALCHEMY_ECHO`: 是否显示SQL语句

### 日志配置
- `LOG_LEVEL`: 日志级别 (DEBUG/INFO/WARNING/ERROR)

## Docker 部署

### 开发环境
```bash
docker-compose -f docker-compose.dev.yml up -d
```

### 生产环境
```bash
docker-compose up -d
```

## 安全注意事项

### 生产环境
1. **必须更改** `SECRET_KEY` 为安全的随机字符串
2. **必须更改** `ADMIN_PASSWORD` 为强密码
3. **必须更新** 所有平台的API密钥为有效密钥
4. **必须设置** `SESSION_COOKIE_SECURE=true`
5. **建议启用** HTTPS (`PREFERRED_URL_SCHEME=https`)

### 文件权限
```bash
# 设置配置文件权限（仅所有者可读写）
chmod 600 .env.production
chmod 600 .env.development
```

### 版本控制
- `.env.example` - 可以提交到版本控制
- `.env.*` - 不应提交到版本控制（已在 .gitignore 中）

## 故障排除

### 常见问题
1. **配置文件未找到**: 确保文件名正确，路径正确
2. **数据库连接失败**: 检查 `DATABASE_URL` 配置
3. **API密钥无效**: 更新平台配置中的API密钥
4. **权限错误**: 检查文件权限设置

### 调试配置
```bash
# 查看当前加载的环境变量
python -c "
import os
from dotenv import load_dotenv
load_dotenv('.env.development')
print('FLASK_ENV:', os.getenv('FLASK_ENV'))
print('DATABASE_URL:', os.getenv('DATABASE_URL'))
"
```
