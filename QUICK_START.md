# 快速开始指南 - Docker Compose V2

本项目已完全适配 Docker Compose V2，提供了简化的部署流程。

## 🚀 一键启动

### 方法 1: 使用启动脚本（推荐）
```bash
./start.sh
```
脚本会引导您选择环境并自动完成部署。

### 方法 2: 使用 Makefile
```bash
# 生产环境
make deploy

# 开发环境  
make dev-deploy
```

### 方法 3: 手动部署

#### 生产环境
```bash
# 1. 准备配置
cp .env.production .env

# 2. 编辑配置（重要！）
nano .env

# 3. 启动服务
docker compose up -d

# 4. 检查状态
docker compose ps
```

#### 开发环境
```bash
# 1. 准备配置
cp .env.development .env.dev

# 2. 启动服务
docker compose -f docker-compose.dev.yml up -d

# 3. 检查状态
docker compose -f docker-compose.dev.yml ps
```

## 📋 Docker Compose V2 主要变化

### 1. 移除版本声明
- ✅ 新版本：直接从 `services:` 开始
- ❌ 旧版本：需要 `version: '3.8'`

### 2. 命令变化
- ✅ 新版本：`docker compose` (空格)
- ❌ 旧版本：`docker-compose` (连字符)

### 3. 环境变量语法改进
```yaml
# V2 推荐语法
environment:
  FLASK_ENV: production
  PYTHONUNBUFFERED: 1

# 旧语法仍然支持
environment:
  - FLASK_ENV=production
  - PYTHONUNBUFFERED=1
```

### 4. 依赖管理增强
```yaml
depends_on:
  mysql-dev:
    condition: service_healthy  # 等待健康检查通过
```

## 🛠️ 常用操作

### 基本命令
```bash
# 启动服务
docker compose up -d

# 查看状态
docker compose ps

# 查看日志
docker compose logs -f

# 停止服务
docker compose down

# 重启服务
docker compose restart
```

### 使用 Makefile（推荐）
```bash
# 查看所有可用命令
make help

# 生产环境操作
make up          # 启动
make down        # 停止
make logs        # 查看日志
make shell       # 进入应用容器
make db-shell    # 连接数据库

# 开发环境操作
make dev-up      # 启动开发环境
make dev-down    # 停止开发环境
make dev-logs    # 查看开发环境日志
make dev-shell   # 进入开发容器
```

## 🔧 配置说明

### 重要配置项（生产环境必须修改）
```bash
# 安全配置
SECRET_KEY=CHANGE-THIS-IN-PRODUCTION
ADMIN_PASSWORD=CHANGE-THIS-PASSWORD

# 数据库配置
DB_ROOT_PASSWORD=CHANGE-THIS-ROOT-PASSWORD
DB_PASSWORD=CHANGE-THIS-APP-PASSWORD

# API 密钥（如果使用真实服务）
SPARK_API_KEY=your-real-api-key
OPENROUTER_API_KEY=your-real-api-key
DEEPBRICKS_API_KEY=your-real-api-key
```

### 端口配置
- **生产环境**: 
  - 应用: 5000
  - 数据库: 3306
- **开发环境**: 
  - 应用: 5000
  - 数据库: 3307 (避免冲突)

## 📊 服务架构

### 生产环境
```
┌─────────────────┐    ┌──────────────────┐
│  model-registry │────│ model-registry-db│
│     (Flask)     │    │     (MySQL)      │
│     :5000       │    │      :3306       │
└─────────────────┘    └──────────────────┘
```

### 开发环境
```
┌─────────────────────┐    ┌─────────────────┐
│ model-registry-dev  │────│   mysql-dev     │
│   (Flask + 热重载)   │    │    (MySQL)      │
│       :5000         │    │     :3307       │
└─────────────────────┘    └─────────────────┘
```

## 🔍 故障排除

### 检查服务状态
```bash
# 查看所有服务
docker compose ps

# 查看特定服务日志
docker compose logs model-registry
docker compose logs db

# 检查健康状态
curl http://localhost:5000/api/v1/health
```

### 常见问题

1. **端口被占用**
```bash
# 修改端口
export FLASK_RUN_PORT=5001
docker compose up -d
```

2. **数据库连接失败**
```bash
# 检查数据库状态
docker compose logs db
docker compose exec db mysqladmin ping -h localhost
```

3. **配置文件问题**
```bash
# 验证配置
docker compose config
```

4. **完全重置**
```bash
# 停止并删除所有数据
docker compose down -v
docker compose up -d
```

## 📝 访问信息

部署成功后：
- **Web 界面**: http://localhost:5000
- **API 健康检查**: http://localhost:5000/api/v1/health
- **默认管理员密码**: 见配置文件中的 `ADMIN_PASSWORD`

## 🔄 更新部署

```bash
# 拉取最新镜像
docker compose pull

# 重新构建并启动
docker compose up -d --build

# 或使用 Makefile
make update
```

## 📚 更多信息

- 详细配置说明: [CONFIG_README.md](CONFIG_README.md)
- Docker 部署指南: [DOCKER_README.md](DOCKER_README.md)
- 配置管理工具: `python scripts/config_manager.py --help`
