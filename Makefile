# Makefile for Docker Compose V2 operations

.PHONY: help build up down restart logs shell db-shell backup restore clean dev-up dev-down dev-logs

# Default target
help:
	@echo "Available commands:"
	@echo "  Production Environment:"
	@echo "    make up          - Start production services"
	@echo "    make down        - Stop production services"
	@echo "    make restart     - Restart production services"
	@echo "    make logs        - View production logs"
	@echo "    make build       - Build production images"
	@echo ""
	@echo "  Development Environment:"
	@echo "    make dev-up      - Start development services"
	@echo "    make dev-down    - Stop development services"
	@echo "    make dev-logs    - View development logs"
	@echo "    make dev-build   - Build development images"
	@echo ""
	@echo "  Database Operations:"
	@echo "    make db-shell    - Connect to production database"
	@echo "    make dev-db-shell - Connect to development database"
	@echo "    make backup      - Backup production database"
	@echo "    make restore     - Restore production database"
	@echo ""
	@echo "  Utilities:"
	@echo "    make shell       - Access production app shell"
	@echo "    make dev-shell   - Access development app shell"
	@echo "    make clean       - Clean up containers and volumes"
	@echo "    make status      - Show service status"

# Production Environment
up:
	@echo "Starting production services..."
	docker compose up -d

down:
	@echo "Stopping production services..."
	docker compose down

restart:
	@echo "Restarting production services..."
	docker compose restart

logs:
	@echo "Showing production logs..."
	docker compose logs -f

build:
	@echo "Building production images..."
	docker compose build --no-cache

status:
	@echo "Production service status:"
	docker compose ps

# Development Environment
dev-up:
	@echo "Starting development services..."
	docker compose -f docker-compose.dev.yml up -d

dev-down:
	@echo "Stopping development services..."
	docker compose -f docker-compose.dev.yml down

dev-logs:
	@echo "Showing development logs..."
	docker compose -f docker-compose.dev.yml logs -f

dev-build:
	@echo "Building development images..."
	docker compose -f docker-compose.dev.yml build --no-cache

dev-status:
	@echo "Development service status:"
	docker compose -f docker-compose.dev.yml ps

# Database Operations
db-shell:
	@echo "Connecting to production database..."
	docker compose exec db mysql -u root -p

dev-db-shell:
	@echo "Connecting to development database..."
	docker compose -f docker-compose.dev.yml exec mysql-dev mysql -u root -p

backup:
	@echo "Creating database backup..."
	@mkdir -p backups
	docker compose exec db mysqldump -u root -p --single-transaction --routines --triggers model_registry > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "Backup created in backups/ directory"

restore:
	@echo "Restoring database from backup..."
	@read -p "Enter backup file path: " backup_file; \
	docker compose exec -T db mysql -u root -p model_registry < $$backup_file

# Application Shell Access
shell:
	@echo "Accessing production app shell..."
	docker compose exec model-registry bash

dev-shell:
	@echo "Accessing development app shell..."
	docker compose -f docker-compose.dev.yml exec model-registry-dev bash

# Health Checks
health:
	@echo "Checking application health..."
	@curl -f http://localhost:5000/api/v1/health || echo "Health check failed"

dev-health:
	@echo "Checking development application health..."
	@curl -f http://localhost:5000/api/v1/health || echo "Health check failed"

# Cleanup Operations
clean:
	@echo "Cleaning up containers and volumes..."
	docker compose down -v
	docker system prune -f

clean-all:
	@echo "Cleaning up everything (including images)..."
	docker compose down -v --rmi all
	docker system prune -af

# Configuration Management
config-check:
	@echo "Validating docker-compose configuration..."
	docker compose config

dev-config-check:
	@echo "Validating development docker-compose configuration..."
	docker compose -f docker-compose.dev.yml config

# Environment Setup
setup-prod:
	@echo "Setting up production environment..."
	@if [ ! -f .env.production ]; then \
		echo "Creating .env.production from template..."; \
		cp .env.example .env.production; \
		echo "Please edit .env.production with your production settings"; \
	fi
	@echo "Production environment ready"

setup-dev:
	@echo "Setting up development environment..."
	@if [ ! -f .env.development ]; then \
		echo "Creating .env.development from template..."; \
		cp .env.example .env.development; \
		echo "Please edit .env.development with your development settings"; \
	fi
	@echo "Development environment ready"

# Quick deployment
deploy: setup-prod build up
	@echo "Production deployment complete!"
	@echo "Application available at: http://localhost:5000"

dev-deploy: setup-dev dev-build dev-up
	@echo "Development deployment complete!"
	@echo "Application available at: http://localhost:5000"

# Monitoring
monitor:
	@echo "Monitoring container resources..."
	docker stats

# Update operations
update:
	@echo "Updating production services..."
	docker compose pull
	docker compose up -d

dev-update:
	@echo "Updating development services..."
	docker compose -f docker-compose.dev.yml pull
	docker compose -f docker-compose.dev.yml up -d

# Testing
test:
	@echo "Running tests in production environment..."
	docker compose exec model-registry python -m pytest

dev-test:
	@echo "Running tests in development environment..."
	docker compose -f docker-compose.dev.yml exec model-registry-dev python -m pytest
