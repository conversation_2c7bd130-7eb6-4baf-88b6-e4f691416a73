#!/usr/bin/env python3
"""
配置管理工具
用于管理不同环境的配置文件和环境变量
"""

import os
import sys
import argparse
import secrets
from pathlib import Path

def generate_secret_key():
    """生成安全的密钥"""
    return secrets.token_urlsafe(32)

def create_env_file(env_type, overwrite=False):
    """创建环境配置文件"""
    env_file = f".env.{env_type}"
    
    if os.path.exists(env_file) and not overwrite:
        print(f"文件 {env_file} 已存在，使用 --overwrite 参数强制覆盖")
        return False
    
    # 基础配置模板
    base_config = {
        'development': {
            'FLASK_ENV': 'development',
            'FLASK_DEBUG': 'True',
            'SECRET_KEY': 'dev-secret-key-not-for-production',
            'DATABASE_URL': 'mysql+pymysql://root:password@localhost/model_registry_dev',
            'ADMIN_PASSWORD': 'admin123',
            'LOG_LEVEL': 'DEBUG',
            'SESSION_COOKIE_SECURE': 'false',
            'SQLALCHEMY_ECHO': 'true',
            'RATE_LIMIT_ENABLED': 'false',
            'MONITORING_ENABLED': 'false',
            'BACKUP_ENABLED': 'false',
        },
        'production': {
            'FLASK_ENV': 'production',
            'FLASK_DEBUG': 'False',
            'SECRET_KEY': generate_secret_key(),
            'DATABASE_URL': 'mysql+pymysql://root:password@localhost/model_registry',
            'ADMIN_PASSWORD': 'CHANGE-THIS-PASSWORD',
            'LOG_LEVEL': 'WARNING',
            'SESSION_COOKIE_SECURE': 'true',
            'SQLALCHEMY_ECHO': 'false',
            'RATE_LIMIT_ENABLED': 'true',
            'MONITORING_ENABLED': 'true',
            'BACKUP_ENABLED': 'true',
        },
        'testing': {
            'FLASK_ENV': 'testing',
            'FLASK_DEBUG': 'False',
            'SECRET_KEY': 'test-secret-key',
            'DATABASE_URL': 'sqlite:///:memory:',
            'ADMIN_PASSWORD': 'test123',
            'LOG_LEVEL': 'ERROR',
            'SESSION_COOKIE_SECURE': 'false',
            'SQLALCHEMY_ECHO': 'false',
            'RATE_LIMIT_ENABLED': 'false',
            'MONITORING_ENABLED': 'false',
            'BACKUP_ENABLED': 'false',
        }
    }
    
    if env_type not in base_config:
        print(f"不支持的环境类型: {env_type}")
        print(f"支持的类型: {', '.join(base_config.keys())}")
        return False
    
    config = base_config[env_type]
    
    # 写入配置文件
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(f"# {env_type.upper()} 环境配置文件\n")
        f.write(f"# 自动生成于: {os.popen('date').read().strip()}\n\n")
        
        for key, value in config.items():
            f.write(f"{key}={value}\n")
        
        # 添加通用配置
        f.write("\n# API 配置\n")
        f.write("API_ENABLED=true\n")
        f.write("API_VERSION=v1\n")
        
        f.write("\n# 安全设置\n")
        f.write("MAX_LOGIN_ATTEMPTS=5\n")
        f.write("LOGIN_ATTEMPT_TIMEOUT=300\n")
        
        f.write("\n# 分页配置\n")
        f.write("ITEMS_PER_PAGE=20\n")
        
        f.write("\n# 应用运行配置\n")
        f.write("FLASK_RUN_HOST=0.0.0.0\n")
        f.write("FLASK_RUN_PORT=5000\n")
    
    print(f"已创建 {env_file} 配置文件")
    
    if env_type == 'production':
        print("\n⚠️  生产环境配置注意事项:")
        print("1. 请修改 SECRET_KEY 为更安全的值")
        print("2. 请修改 ADMIN_PASSWORD 为强密码")
        print("3. 请更新 DATABASE_URL 为实际的数据库连接")
        print("4. 请检查所有平台的API密钥是否正确")
    
    return True

def validate_env_file(env_file):
    """验证环境配置文件"""
    if not os.path.exists(env_file):
        print(f"配置文件 {env_file} 不存在")
        return False
    
    required_vars = [
        'FLASK_ENV', 'SECRET_KEY', 'DATABASE_URL', 'ADMIN_PASSWORD'
    ]
    
    missing_vars = []
    
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
        
        for var in required_vars:
            if f"{var}=" not in content:
                missing_vars.append(var)
    
    if missing_vars:
        print(f"配置文件 {env_file} 缺少必需的变量:")
        for var in missing_vars:
            print(f"  - {var}")
        return False
    
    print(f"配置文件 {env_file} 验证通过")
    return True

def list_env_files():
    """列出所有环境配置文件"""
    env_files = []
    for file in os.listdir('.'):
        if file.startswith('.env'):
            env_files.append(file)
    
    if env_files:
        print("找到的环境配置文件:")
        for file in sorted(env_files):
            print(f"  - {file}")
    else:
        print("未找到任何环境配置文件")
    
    return env_files

def main():
    parser = argparse.ArgumentParser(description='配置管理工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 创建配置文件命令
    create_parser = subparsers.add_parser('create', help='创建环境配置文件')
    create_parser.add_argument('env_type', choices=['development', 'production', 'testing'],
                              help='环境类型')
    create_parser.add_argument('--overwrite', action='store_true',
                              help='覆盖已存在的文件')
    
    # 验证配置文件命令
    validate_parser = subparsers.add_parser('validate', help='验证环境配置文件')
    validate_parser.add_argument('env_file', help='环境配置文件路径')
    
    # 列出配置文件命令
    list_parser = subparsers.add_parser('list', help='列出所有环境配置文件')
    
    # 生成密钥命令
    key_parser = subparsers.add_parser('generate-key', help='生成安全密钥')
    
    args = parser.parse_args()
    
    if args.command == 'create':
        create_env_file(args.env_type, args.overwrite)
    elif args.command == 'validate':
        validate_env_file(args.env_file)
    elif args.command == 'list':
        list_env_files()
    elif args.command == 'generate-key':
        print(f"生成的安全密钥: {generate_secret_key()}")
    else:
        parser.print_help()

if __name__ == '__main__':
    main()
