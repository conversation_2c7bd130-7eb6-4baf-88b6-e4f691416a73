# 开发环境配置文件

# Flask 环境配置
FLASK_ENV=development
FLASK_DEBUG=True

# 安全配置 - 开发环境使用简单密钥
SECRET_KEY=dev-secret-key-not-for-production

# 数据库配置 - 开发环境使用Docker容器数据库
DATABASE_URL=mysql+pymysql://app_user:app_password@mysql-dev/model_registry_dev

# 管理员配置 - 开发环境使用简单密码
ADMIN_PASSWORD=@Rw80827

# API 配置
API_ENABLED=true
API_VERSION=v1

# 安全设置 - 开发环境宽松设置
MAX_LOGIN_ATTEMPTS=10
LOGIN_ATTEMPT_TIMEOUT=60  # 1分钟

# 分页配置
ITEMS_PER_PAGE=10  # 开发环境使用较小分页

# 日志配置 - 开发环境使用DEBUG级别
LOG_LEVEL=DEBUG

# 会话配置 - 开发环境设置
SESSION_COOKIE_SECURE=false
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Lax

# 数据库配置 - 开发环境显示SQL
SQLALCHEMY_ECHO=true

# 数据库连接池配置 - 开发环境较小配置
DB_POOL_SIZE=5
DB_POOL_TIMEOUT=10
DB_POOL_RECYCLE=-1
DB_POOL_PRE_PING=true

# 速率限制配置 - 开发环境禁用或宽松
RATE_LIMIT_ENABLED=false
RATE_LIMIT_DEFAULT=1000
RATE_LIMIT_WINDOW=3600

# 文件上传配置
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=uploads

# 缓存配置
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=60

# 监控配置 - 开发环境禁用
MONITORING_ENABLED=false
METRICS_ENDPOINT=/metrics

# 备份配置 - 开发环境禁用
BACKUP_ENABLED=false
BACKUP_INTERVAL=86400
BACKUP_RETENTION=3

# 预设平台配置 - 开发环境启用以便测试
INIT_PLATFORMS_ENABLED=true

# Spark平台配置 - 开发环境可以使用测试密钥
SPARK_PLATFORM_NAME=spark
SPARK_BASE_URL=https://spark-api-open.xf-yun.com/v1
SPARK_API_KEY=test-api-key-for-development

# OpenRouter平台配置
OPENROUTER_PLATFORM_NAME=OpenRouter
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_API_KEY=test-api-key-for-development

# DeepBricks平台配置
DEEPBRICKS_PLATFORM_NAME=deepbricks
DEEPBRICKS_BASE_URL=https://api.deepbricks.ai/v1/
DEEPBRICKS_API_KEY=test-api-key-for-development

# 应用运行配置
FLASK_RUN_HOST=127.0.0.1
FLASK_RUN_PORT=5000

# Docker数据库配置（开发环境）
DB_ROOT_PASSWORD=password
DB_NAME=model_registry_dev
DB_USER=app_user
DB_PASSWORD=app_password
DB_DEV_PORT=3307

# 开发工具配置
FLASK_RUN_RELOAD=true
FLASK_RUN_DEBUGGER=true
