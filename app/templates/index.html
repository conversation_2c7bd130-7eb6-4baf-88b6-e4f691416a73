{% extends "base.html" %}

{% block title %}首页 - 大语言模型管理系统{% endblock %}

{% block header %}欢迎使用大语言模型管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">系统概述</h5>
                <p class="card-text">
                    本系统用于管理大语言模型的平台、模型和应用模型关联，实现多个应用共享同一套大语言模型数据。
                </p>
                <p class="card-text">
                    您可以通过左侧导航栏访问各个功能模块。
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">平台管理</h5>
                <p class="card-text">管理大语言模型的平台信息，包括平台名称、API地址和密钥等。</p>
                <a href="{{ url_for('main.list_platforms') }}" class="btn btn-primary">进入管理</a>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">模型管理</h5>
                <p class="card-text">管理各平台提供的大语言模型，包括模型名称、内部标识和价格等信息。</p>
                <a href="{{ url_for('main.list_models') }}" class="btn btn-primary">进入管理</a>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">应用管理</h5>
                <p class="card-text">管理使用大语言模型的应用程序，为每个应用分配唯一的API密钥。</p>
                <a href="{{ url_for('main.list_applications') }}" class="btn btn-primary">进入管理</a>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">应用模型关联</h5>
                <p class="card-text">管理应用程序可以使用的模型列表，设置默认模型等。</p>
                <a href="{{ url_for('main.list_app_models') }}" class="btn btn-primary">进入管理</a>
            </div>
        </div>
    </div>
</div>
    <!-- 在templates/index.html中添加API状态显示 -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">系统状态</h5>
                <p class="card-text">
                    API功能状态: 
                    {% if api_enabled %}
                    <span class="badge bg-success">已启用</span>
                    {% else %}
                    <span class="badge bg-danger">已禁用</span>
                    {% endif %}
                </p>
                <p class="card-text">
                    <small class="text-muted">API状态可通过环境变量 API_ENABLED 控制</small>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
