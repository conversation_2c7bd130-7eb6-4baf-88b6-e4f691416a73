{% extends "base.html" %}

{% block title %}应用管理 - 大语言模型管理系统{% endblock %}

{% block header %}应用管理{% endblock %}

{% block content %}
<div class="d-flex justify-content-between mb-3">
    <h3>应用列表</h3>
    <a href="{{ url_for('main.add_application') }}" class="btn btn-primary">
        <i class="bi bi-plus"></i> 添加应用
    </a>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>应用名称</th>
                        <th>描述</th>
                        <th>API密钥</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for app in applications %}
                    <tr>
                        <td>{{ app.id }}</td>
                        <td>{{ app.name }}</td>
                        <td>{{ app.description }}</td>
                        <td>
                            <div class="input-group">
                                <input type="text" class="form-control form-control-sm" value="{{ app.api_key }}" readonly>
                                <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" 
                                        data-clipboard-text="{{ app.api_key }}">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                            </div>
                        </td>
                        <td>{{ app.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                        <td>
                            <a href="{{ url_for('main.edit_application', id=app.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-pencil"></i> 编辑
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                    data-bs-toggle="modal" data-bs-target="#deleteModal{{ app.id }}">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                            <a href="{{ url_for('main.manage_app_models', id=app.id) }}" class="btn btn-sm btn-outline-info">
                               <i class="bi bi-gear"></i> 管理模型
                            </a>
                             
                            <!-- 删除确认对话框 -->
                            <div class="modal fade" id="deleteModal{{ app.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">确认删除</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            确定要删除应用 "{{ app.name }}" 吗？此操作不可逆，将同时删除与此应用关联的所有模型关联。
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                            <form action="{{ url_for('main.delete_application', id=app.id) }}" method="post">
                                                <button type="submit" class="btn btn-danger">确认删除</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="6" class="text-center">暂无应用数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/clipboard@2.0.11/dist/clipboard.min.js"></script>
<script>
    // 初始化剪贴板功能
    var clipboard = new ClipboardJS('.copy-btn');

    clipboard.on('success', function(e) {
        // 显示复制成功提示
        var button = e.trigger;
        var originalHtml = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i>';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');

        setTimeout(function() {
            button.innerHTML = originalHtml;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);

        e.clearSelection();
    });
</script>
{% endblock %}
