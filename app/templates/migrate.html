{% extends "base.html" %}

{% block title %}数据迁移 - 大语言模型管理系统{% endblock %}

{% block header %}数据迁移{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <h5 class="card-title">从SQLite迁移数据到MariaDB</h5>
        <p class="card-text">
            此操作将从SQLite数据库（chat_system.db）迁移平台和模型数据到当前MariaDB数据库。
            迁移过程不会删除现有数据，只会添加不存在的记录。
        </p>

        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i> 请确保SQLite数据库文件位于正确的位置，并且MariaDB数据库已正确配置。
        </div>

        <form method="post" action="{{ url_for('main.migrate_data') }}">
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-arrow-left-right"></i> 开始迁移
                </button>
                <a href="{{ url_for('main.index') }}" class="btn btn-secondary">返回首页</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
