import os
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, ForeignKey, Numeric, Boolean
from sqlalchemy.orm import declarative_base, relationship, sessionmaker, scoped_session
from datetime import datetime
import logging

# 日志配置
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建基类和会话
Base = declarative_base()
# 在db_models.py中找到创建引擎的代码

# 获取环境变量中的数据库路径，如果没有设置则使用默认值
db_path = os.environ.get('DATABASE_PATH', 'database/chat_system.db')

# 确保目录存在
db_dir = os.path.dirname(db_path)
if db_dir and not os.path.exists(db_dir):
    os.makedirs(db_dir, exist_ok=True)

# 构建 SQLAlchemy URI
if os.name == 'nt':  # Windows
    # Windows下，如果是绝对路径，需要添加额外的斜杠
    if os.path.isabs(db_path):
        db_uri = f'sqlite:///{db_path}'
    else:
        db_uri = f'sqlite:///{db_path}'
else:  # Unix/Linux/Mac
    db_uri = f'sqlite:///{db_path}'

# 创建引擎
engine = create_engine(
    db_uri,
    pool_size=10,
    max_overflow=15,
    pool_timeout=60,
    pool_pre_ping=True
)
SessionLocal = sessionmaker(bind=engine)
db_session = scoped_session(SessionLocal)
# 数据库会话管理
def get_db():
    """数据库会话上下文管理器"""
    try:
        yield db_session
    finally:
        db_session.remove()


# 模型定义
class Platform(Base):
    __tablename__ = 'platforms'

    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)
    base_url = Column(String(255), nullable=False)
    api_key = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    models = relationship("AIModel", back_populates="platform")

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'base_url': self.base_url,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class AIModel(Base):
    __tablename__ = 'ai_models'
    id = Column(Integer, primary_key=True)
    display_name = Column(String(50), unique=True, nullable=False)
    internal_name = Column(String(100), nullable=False)
    api_endpoint = Column(String(255))
    created_at = Column(DateTime, default=datetime.utcnow)

    # 新增价格字段 (每1000个token的价格，单位为美元)
    input_token_price = Column(Numeric(10, 6), default=0.000000)  # 输入token价格
    output_token_price = Column(Numeric(10, 6), default=0.000000)  # 输出token价格
    input_picture_price = Column(Numeric(10, 6), default=0.000000)  # 输入图片价格
    is_visible_model = Column(Boolean, default=False)
    free = Column(Boolean, default=False)  # 是否免费
    high_price = Column(Boolean, default=False)  # 是否高价
    platform_id = Column(Integer, ForeignKey('platforms.id'), nullable=False)
    platform = relationship("Platform", back_populates="models", foreign_keys=[platform_id])

    def to_dict(self):
        return {
            'id': self.id,
            'display_name': self.display_name,
            'platform': self.platform.name,
            'internal_name': self.internal_name,
            'is_visible_model': self.is_visible_model,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'input_token_price': float(self.input_token_price),
            'output_token_price': float(self.output_token_price),
            'input_picture_price': float(self.input_picture_price),
            'free': self.free,
            'high_price': self.high_price
        }
class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    api_key = Column(String(255), unique=True, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    permission = Column(Integer, default=1, nullable=False)
    # 上面是基础信息
    mathjax = Column(Boolean, default=False)
    current_model_id = Column(Integer, ForeignKey('ai_models.id'))
    current_temperature = Column(Numeric(3, 2), default=0.7)
    current_conversation_id = Column(Integer, ForeignKey('conversations.id'))
    # 新增余额相关字段
    total_deposited = Column(Numeric(10, 4), default=0.0000)  # 总充值金额
    total_spent = Column(Numeric(10, 4), default=0.0000)  # 总消费金额
    current_balance = Column(Numeric(10, 4), default=0.0000)  # 当前余额

    # 新增token使用统计
    total_prompt_tokens = Column(Integer, default=0)  # 总输入token数
    total_completion_tokens = Column(Integer, default=0)  # 总输出token数

    # 关系定义
    current_model = relationship("AIModel", foreign_keys=[current_model_id])
    conversations = relationship("Conversation",
                                 back_populates="user",
                                 foreign_keys="[Conversation.user_id]")
    current_conversation = relationship("Conversation",
                                        foreign_keys=[current_conversation_id])

    def to_dict(self):
        return {
            #  基本信息
            "id": self.id,
            "api_key": self.api_key,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "is_active": self.is_active,
            "permission": self.permission,
            # 设置信息
            "mathjax": self.mathjax,
            "current_model_id": self.current_model_id,
            "current_temperature": float(self.current_temperature) if self.current_temperature else 0.7,
            "current_conversation_id": self.current_conversation_id,
            # 费用相关字段
            "total_deposited": float(self.total_deposited) if self.total_deposited else 0.0,
            "total_spent": float(self.total_spent) if self.total_spent else 0.0,
            "current_balance": float(self.current_balance) if self.current_balance else 0.0,
            "total_prompt_tokens": self.total_prompt_tokens,
            "total_completion_tokens": self.total_completion_tokens
        }


class Conversation(Base):
    __tablename__ = 'conversations'
    # 基本信息
    id = Column(Integer, primary_key=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    latest_revised_at = Column(DateTime, default=datetime.utcnow)

    # 纪录
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    title = Column(String(255))

    # 关系定义
    user = relationship("User",
                        back_populates="conversations",
                        foreign_keys=[user_id])
    messages = relationship("Message",
                            back_populates="conversation",
                            cascade="all, delete-orphan")

    def to_dict(self):
        return {
            # 基本信息
            'id': self.id,
            'created_at': self.created_at.isoformat(),
            'latest_revised_at': self.latest_revised_at.isoformat(),
            # 记录
            'title': self.title,
            'user_id': self.user_id,
            'message_count': len(self.messages)
        }

    def update_title_from_message(self, user_message_content):
        if user_message_content:
            max_length = 30
            self.title = (user_message_content[:max_length] + '...'
                          if len(user_message_content) > max_length
                          else user_message_content)


class Message(Base):
    __tablename__ = 'messages'

    id = Column(Integer, primary_key=True)
    conversation_id = Column(Integer, ForeignKey('conversations.id'), nullable=False)
    role = Column(String(20))
    content = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 记录模型ID和温度
    model_id = Column(Integer, ForeignKey('ai_models.id'), nullable=False)
    temperature = Column(Numeric(3, 2))
    max_tokens = Column(Integer)

    # 记录token使用情况
    prompt_tokens = Column(Integer)  # 输入token数量
    completion_tokens = Column(Integer)  # 输出token数量

    # 新增费用字段
    prompt_cost = Column(Numeric(10, 6), default=0.000000)  # 输入token费用
    completion_cost = Column(Numeric(10, 6), default=0.000000)  # 输出token费用
    total_cost = Column(Numeric(10, 6), default=0.000000)  # 总费用

    is_error = Column(Boolean, default=False)
    error_info = Column(Text)

    conversation = relationship("Conversation", back_populates="messages")
    model = relationship(argument="AIModel", foreign_keys=[model_id])

    def to_dict(self):
        return {
            'id': self.id,
            'conversation_id': self.conversation_id,
            'role': self.role,
            'content': self.content,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            # model相关信息
            'model_id': self.model_id,
            'temperature': float(self.temperature) if self.temperature else None,
            'max_tokens': self.max_tokens,
            # 费用相关信息
            'prompt_tokens': self.prompt_tokens,
            'completion_tokens': self.completion_tokens,
            'prompt_cost': float(self.prompt_cost) if self.prompt_cost else 0,
            'completion_cost': float(self.completion_cost) if self.completion_cost else 0,
            'total_cost': float(self.total_cost) if self.total_cost else 0,
            'is_error': self.is_error,
            'error_info': self.error_info
        }


class Transaction(Base):
    __tablename__ = 'transactions'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    transaction_type = Column(String(20), nullable=False)  # 'deposit' 或 'consumption'
    amount = Column(Numeric(10, 4), nullable=False)
    balance_after = Column(Numeric(10, 4), nullable=False)  # 交易后余额
    description = Column(String(255))
    message_id = Column(Integer, ForeignKey('messages.id'))  # 如果是消费，关联到具体消息
    created_at = Column(DateTime, default=datetime.utcnow)

    # 关系
    user = relationship("User")
    message = relationship("Message")

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'transaction_type': self.transaction_type,
            'amount': float(self.amount),
            'balance_after': float(self.balance_after),
            'description': self.description,
            'message_id': self.message_id,
            'created_at': self.created_at.isoformat()
        }
