2025-07-12 12:35:17,594 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT DATABASE()
2025-07-12 12:35:17,594 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,597 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@sql_mode
2025-07-12 12:35:17,597 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,597 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@lower_case_table_names
2025-07-12 12:35:17,598 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,598 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:35:17,599 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`platforms`
2025-07-12 12:35:17,599 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,605 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`ai_models`
2025-07-12 12:35:17,606 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,607 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`applications`
2025-07-12 12:35:17,608 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,609 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`app_models`
2025-07-12 12:35:17,609 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,611 - sqlalchemy.engine.Engine - INFO - base.py:2704 - _connection_commit_impl - COMMIT
2025-07-12 12:35:17,636 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT DATABASE()
2025-07-12 12:35:17,636 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT DATABASE()
2025-07-12 12:35:17,637 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,637 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,638 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@sql_mode
2025-07-12 12:35:17,638 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@sql_mode
2025-07-12 12:35:17,638 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,638 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,638 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@lower_case_table_names
2025-07-12 12:35:17,638 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@lower_case_table_names
2025-07-12 12:35:17,639 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,639 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,639 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:35:17,639 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:35:17,640 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`platforms`
2025-07-12 12:35:17,640 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`platforms`
2025-07-12 12:35:17,640 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,640 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,642 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`ai_models`
2025-07-12 12:35:17,642 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`ai_models`
2025-07-12 12:35:17,642 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,642 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,643 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`applications`
2025-07-12 12:35:17,643 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`applications`
2025-07-12 12:35:17,643 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,643 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,645 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`app_models`
2025-07-12 12:35:17,645 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`app_models`
2025-07-12 12:35:17,645 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,645 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,647 - sqlalchemy.engine.Engine - INFO - base.py:2704 - _connection_commit_impl - COMMIT
2025-07-12 12:35:17,647 - sqlalchemy.engine.Engine - INFO - base.py:2704 - _connection_commit_impl - COMMIT
2025-07-12 12:35:17,657 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:35:17,657 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:35:17,659 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT platforms.id AS platforms_id, platforms.name AS platforms_name, platforms.base_url AS platforms_base_url, platforms.api_key AS platforms_api_key, platforms.created_at AS platforms_created_at, platforms.updated_at AS platforms_updated_at 
FROM platforms
2025-07-12 12:35:17,659 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT platforms.id AS platforms_id, platforms.name AS platforms_name, platforms.base_url AS platforms_base_url, platforms.api_key AS platforms_api_key, platforms.created_at AS platforms_created_at, platforms.updated_at AS platforms_updated_at 
FROM platforms
2025-07-12 12:35:17,659 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [generated in 0.00028s] {}
2025-07-12 12:35:17,659 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [generated in 0.00028s] {}
2025-07-12 12:35:17,660 - sqlalchemy.engine.Engine - INFO - base.py:2701 - _connection_rollback_impl - ROLLBACK
2025-07-12 12:35:17,660 - sqlalchemy.engine.Engine - INFO - base.py:2701 - _connection_rollback_impl - ROLLBACK
2025-07-12 12:39:26,329 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT DATABASE()
2025-07-12 12:39:26,329 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,330 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@sql_mode
2025-07-12 12:39:26,330 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,331 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@lower_case_table_names
2025-07-12 12:39:26,331 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,332 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:39:26,332 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`platforms`
2025-07-12 12:39:26,333 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,334 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`ai_models`
2025-07-12 12:39:26,335 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,336 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`applications`
2025-07-12 12:39:26,336 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,338 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`app_models`
2025-07-12 12:39:26,338 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,339 - sqlalchemy.engine.Engine - INFO - base.py:2704 - _connection_commit_impl - COMMIT
2025-07-12 12:39:26,362 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT DATABASE()
2025-07-12 12:39:26,362 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT DATABASE()
2025-07-12 12:39:26,363 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,363 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,364 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@sql_mode
2025-07-12 12:39:26,364 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@sql_mode
2025-07-12 12:39:26,364 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,364 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,364 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@lower_case_table_names
2025-07-12 12:39:26,364 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@lower_case_table_names
2025-07-12 12:39:26,364 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,364 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,365 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:39:26,365 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:39:26,365 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`platforms`
2025-07-12 12:39:26,365 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`platforms`
2025-07-12 12:39:26,366 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,366 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,367 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`ai_models`
2025-07-12 12:39:26,367 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`ai_models`
2025-07-12 12:39:26,368 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,368 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,369 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`applications`
2025-07-12 12:39:26,369 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`applications`
2025-07-12 12:39:26,370 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,370 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,371 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`app_models`
2025-07-12 12:39:26,371 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`app_models`
2025-07-12 12:39:26,371 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,371 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:26,373 - sqlalchemy.engine.Engine - INFO - base.py:2704 - _connection_commit_impl - COMMIT
2025-07-12 12:39:26,373 - sqlalchemy.engine.Engine - INFO - base.py:2704 - _connection_commit_impl - COMMIT
2025-07-12 12:39:26,383 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:39:26,383 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:39:26,385 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT platforms.id AS platforms_id, platforms.name AS platforms_name, platforms.base_url AS platforms_base_url, platforms.api_key AS platforms_api_key, platforms.created_at AS platforms_created_at, platforms.updated_at AS platforms_updated_at 
FROM platforms
2025-07-12 12:39:26,385 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT platforms.id AS platforms_id, platforms.name AS platforms_name, platforms.base_url AS platforms_base_url, platforms.api_key AS platforms_api_key, platforms.created_at AS platforms_created_at, platforms.updated_at AS platforms_updated_at 
FROM platforms
2025-07-12 12:39:26,386 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [generated in 0.00031s] {}
2025-07-12 12:39:26,386 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [generated in 0.00031s] {}
2025-07-12 12:39:26,389 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT ai_models.id AS ai_models_id, ai_models.display_name AS ai_models_display_name, ai_models.internal_name AS ai_models_internal_name, ai_models.api_endpoint AS ai_models_api_endpoint, ai_models.created_at AS ai_models_created_at, ai_models.updated_at AS ai_models_updated_at, ai_models.input_token_price AS ai_models_input_token_price, ai_models.output_token_price AS ai_models_output_token_price, ai_models.input_picture_price AS ai_models_input_picture_price, ai_models.is_visible_model AS ai_models_is_visible_model, ai_models.free AS ai_models_free, ai_models.high_price AS ai_models_high_price, ai_models.platform_id AS ai_models_platform_id 
FROM ai_models
2025-07-12 12:39:26,389 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT ai_models.id AS ai_models_id, ai_models.display_name AS ai_models_display_name, ai_models.internal_name AS ai_models_internal_name, ai_models.api_endpoint AS ai_models_api_endpoint, ai_models.created_at AS ai_models_created_at, ai_models.updated_at AS ai_models_updated_at, ai_models.input_token_price AS ai_models_input_token_price, ai_models.output_token_price AS ai_models_output_token_price, ai_models.input_picture_price AS ai_models_input_picture_price, ai_models.is_visible_model AS ai_models_is_visible_model, ai_models.free AS ai_models_free, ai_models.high_price AS ai_models_high_price, ai_models.platform_id AS ai_models_platform_id 
FROM ai_models
2025-07-12 12:39:26,389 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [generated in 0.00030s] {}
2025-07-12 12:39:26,389 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [generated in 0.00030s] {}
2025-07-12 12:39:26,392 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT applications.id AS applications_id, applications.name AS applications_name, applications.description AS applications_description, applications.api_key AS applications_api_key, applications.created_at AS applications_created_at, applications.updated_at AS applications_updated_at, applications.is_active AS applications_is_active 
FROM applications
2025-07-12 12:39:26,392 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT applications.id AS applications_id, applications.name AS applications_name, applications.description AS applications_description, applications.api_key AS applications_api_key, applications.created_at AS applications_created_at, applications.updated_at AS applications_updated_at, applications.is_active AS applications_is_active 
FROM applications
2025-07-12 12:39:26,392 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [generated in 0.00026s] {}
2025-07-12 12:39:26,392 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [generated in 0.00026s] {}
2025-07-12 12:39:26,393 - sqlalchemy.engine.Engine - INFO - base.py:2701 - _connection_rollback_impl - ROLLBACK
2025-07-12 12:39:26,393 - sqlalchemy.engine.Engine - INFO - base.py:2701 - _connection_rollback_impl - ROLLBACK
2025-07-12 12:39:38,118 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT DATABASE()
2025-07-12 12:39:38,118 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,120 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@sql_mode
2025-07-12 12:39:38,120 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,121 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@lower_case_table_names
2025-07-12 12:39:38,121 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,122 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:39:38,122 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`platforms`
2025-07-12 12:39:38,123 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,125 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`ai_models`
2025-07-12 12:39:38,125 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,127 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`applications`
2025-07-12 12:39:38,127 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,128 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`app_models`
2025-07-12 12:39:38,128 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,130 - sqlalchemy.engine.Engine - INFO - base.py:2704 - _connection_commit_impl - COMMIT
2025-07-12 12:39:38,138 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:39:38,139 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`platforms`
2025-07-12 12:39:38,139 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,140 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`ai_models`
2025-07-12 12:39:38,140 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,142 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`applications`
2025-07-12 12:39:38,142 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,143 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`app_models`
2025-07-12 12:39:38,144 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,145 - sqlalchemy.engine.Engine - INFO - base.py:2704 - _connection_commit_impl - COMMIT
2025-07-12 12:39:38,610 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT DATABASE()
2025-07-12 12:39:38,610 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,611 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@sql_mode
2025-07-12 12:39:38,611 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,612 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@lower_case_table_names
2025-07-12 12:39:38,612 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,613 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:39:38,613 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`platforms`
2025-07-12 12:39:38,614 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,615 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`ai_models`
2025-07-12 12:39:38,616 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,617 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`applications`
2025-07-12 12:39:38,617 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,618 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`app_models`
2025-07-12 12:39:38,619 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,620 - sqlalchemy.engine.Engine - INFO - base.py:2704 - _connection_commit_impl - COMMIT
2025-07-12 12:39:38,628 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:39:38,628 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`platforms`
2025-07-12 12:39:38,628 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,630 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`ai_models`
2025-07-12 12:39:38,630 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,631 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`applications`
2025-07-12 12:39:38,631 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,632 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`app_models`
2025-07-12 12:39:38,633 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:39:38,634 - sqlalchemy.engine.Engine - INFO - base.py:2704 - _connection_commit_impl - COMMIT
