from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app
from models import db, Platform, AIModel, Application, AppModel
from services.database import PlatformService, ModelService, ApplicationService, AppModelService
from utils.security import login_required, security_manager
from utils.validators import PlatformForm, ModelForm, ApplicationForm, AppModelForm
import logging

logger = logging.getLogger(__name__)

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
@login_required
def index():
    """首页"""
    api_enabled = current_app.config.get('API_ENABLED', False)
    
    # 获取统计信息
    stats = {
        'platforms_count': Platform.query.count(),
        'models_count': AIModel.query.count(),
        'applications_count': Application.query.count(),
        'app_models_count': AppModel.query.count()
    }
    
    return render_template('index.html', api_enabled=api_enabled, stats=stats)

# ======== 平台管理 ========
@main_bp.route('/platforms')
@login_required
def list_platforms():
    """平台列表"""
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)

    platforms = PlatformService.get_all(page=page, per_page=per_page)

    return render_template('platforms/list.html', platforms=platforms)

@main_bp.route('/platforms/add', methods=['GET', 'POST'])
@login_required
def add_platform():
    """添加平台"""
    form = PlatformForm()

    if form.validate_on_submit():
        success, error, platform = PlatformService.create(
            name=form.name.data,
            base_url=form.base_url.data,
            api_key=form.api_key.data
        )

        if success:
            flash('平台添加成功', 'success')
            return redirect(url_for('main.list_platforms'))
        else:
            flash(error or '添加平台时发生错误', 'danger')

    return render_template('platforms/add.html', form=form)

@main_bp.route('/platforms/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_platform(id):
    """编辑平台"""
    platform = PlatformService.get_by_id(id)
    if not platform:
        flash('平台不存在', 'danger')
        return redirect(url_for('main.list_platforms'))

    form = PlatformForm(obj=platform)

    if form.validate_on_submit():
        success, error, updated_platform = PlatformService.update(
            platform_id=id,
            name=form.name.data,
            base_url=form.base_url.data,
            api_key=form.api_key.data
        )

        if success:
            flash('平台更新成功', 'success')
            return redirect(url_for('main.list_platforms'))
        else:
            flash(error or '更新平台时发生错误', 'danger')

    return render_template('platforms/edit.html', form=form, platform=platform)

@main_bp.route('/platforms/delete/<int:id>', methods=['POST'])
@login_required
def delete_platform(id):
    """删除平台"""
    success, error = PlatformService.delete(id)

    if success:
        flash('平台删除成功', 'success')
    else:
        flash(error or '删除平台时发生错误', 'danger')

    return redirect(url_for('main.list_platforms'))

# ======== 模型管理 ========
@main_bp.route('/models')
@login_required
def list_models():
    """模型列表"""
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
    
    models = AIModel.query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('models/list.html', models=models)

@main_bp.route('/models/add', methods=['GET', 'POST'])
@login_required
def add_model():
    """添加模型"""
    form = ModelForm()
    
    # 设置平台选择项
    platforms = Platform.query.all()
    form.platform_id.choices = [(p.id, p.name) for p in platforms]
    
    if form.validate_on_submit():
        try:
            model = AIModel(
                display_name=form.display_name.data,
                internal_name=form.internal_name.data,
                api_endpoint=form.api_endpoint.data,
                platform_id=form.platform_id.data,
                input_token_price=form.input_token_price.data or 0,
                output_token_price=form.output_token_price.data or 0,
                input_picture_price=form.input_picture_price.data or 0,
                is_visible_model=form.is_visible_model.data,
                free=form.free.data,
                high_price=form.high_price.data
            )
            
            db.session.add(model)
            db.session.commit()
            
            flash('模型添加成功', 'success')
            logger.info(f"Model added: {model.display_name}")
            return redirect(url_for('main.list_models'))
            
        except Exception as e:
            db.session.rollback()
            flash('添加模型时发生错误', 'danger')
            logger.error(f"Error adding model: {str(e)}")
    
    return render_template('models/add.html', form=form)

@main_bp.route('/models/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_model(id):
    """编辑模型"""
    model = AIModel.query.get_or_404(id)
    form = ModelForm(obj=model)

    # 设置平台选择项
    platforms = Platform.query.all()
    form.platform_id.choices = [(p.id, p.name) for p in platforms]

    if form.validate_on_submit():
        try:
            # 检查显示名称唯一性（排除当前记录）
            existing = AIModel.query.filter(
                AIModel.display_name == form.display_name.data,
                AIModel.id != id
            ).first()

            if existing:
                flash('显示名称已存在', 'danger')
                return render_template('models/edit.html', form=form, model=model)

            model.display_name = form.display_name.data
            model.internal_name = form.internal_name.data
            model.api_endpoint = form.api_endpoint.data
            model.platform_id = form.platform_id.data
            model.input_token_price = form.input_token_price.data or 0
            model.output_token_price = form.output_token_price.data or 0
            model.input_picture_price = form.input_picture_price.data or 0
            model.is_visible_model = form.is_visible_model.data
            model.free = form.free.data
            model.high_price = form.high_price.data

            db.session.commit()

            flash('模型更新成功', 'success')
            logger.info(f"Model updated: {model.display_name}")
            return redirect(url_for('main.list_models'))

        except Exception as e:
            db.session.rollback()
            flash('更新模型时发生错误', 'danger')
            logger.error(f"Error updating model: {str(e)}")

    return render_template('models/edit.html', form=form, model=model)

@main_bp.route('/models/delete/<int:id>', methods=['POST'])
@login_required
def delete_model(id):
    """删除模型"""
    model = AIModel.query.get_or_404(id)

    try:
        # 检查是否有关联的应用模型
        if model.app_models:
            flash('无法删除模型，存在关联的应用', 'danger')
            return redirect(url_for('main.list_models'))

        db.session.delete(model)
        db.session.commit()

        flash('模型删除成功', 'success')
        logger.info(f"Model deleted: {model.display_name}")

    except Exception as e:
        db.session.rollback()
        flash('删除模型时发生错误', 'danger')
        logger.error(f"Error deleting model: {str(e)}")

    return redirect(url_for('main.list_models'))

# ======== 应用管理 ========
@main_bp.route('/applications')
@login_required
def list_applications():
    """应用列表"""
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)

    applications = Application.query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    return render_template('applications/list.html', applications=applications)

@main_bp.route('/applications/add', methods=['GET', 'POST'])
@login_required
def add_application():
    """添加应用"""
    form = ApplicationForm()

    if form.validate_on_submit():
        try:
            # 生成API密钥
            api_key = security_manager.generate_api_key()

            application = Application(
                name=form.name.data,
                description=form.description.data,
                api_key=api_key
            )

            db.session.add(application)
            db.session.commit()

            flash('应用添加成功', 'success')
            logger.info(f"Application added: {application.name}")
            return redirect(url_for('main.list_applications'))

        except Exception as e:
            db.session.rollback()
            flash('添加应用时发生错误', 'danger')
            logger.error(f"Error adding application: {str(e)}")

    return render_template('applications/add.html', form=form)

@main_bp.route('/applications/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_application(id):
    """编辑应用"""
    application = Application.query.get_or_404(id)
    form = ApplicationForm(obj=application)

    if form.validate_on_submit():
        try:
            # 检查名称唯一性（排除当前记录）
            existing = Application.query.filter(
                Application.name == form.name.data,
                Application.id != id
            ).first()

            if existing:
                flash('应用名称已存在', 'danger')
                return render_template('applications/edit.html', form=form, application=application)

            application.name = form.name.data
            application.description = form.description.data

            db.session.commit()

            flash('应用更新成功', 'success')
            logger.info(f"Application updated: {application.name}")
            return redirect(url_for('main.list_applications'))

        except Exception as e:
            db.session.rollback()
            flash('更新应用时发生错误', 'danger')
            logger.error(f"Error updating application: {str(e)}")

    return render_template('applications/edit.html', form=form, application=application)

@main_bp.route('/applications/delete/<int:id>', methods=['POST'])
@login_required
def delete_application(id):
    """删除应用"""
    application = Application.query.get_or_404(id)

    try:
        # 检查是否有关联的应用模型
        if application.app_models:
            flash('无法删除应用，存在关联的模型', 'danger')
            return redirect(url_for('main.list_applications'))

        db.session.delete(application)
        db.session.commit()

        flash('应用删除成功', 'success')
        logger.info(f"Application deleted: {application.name}")

    except Exception as e:
        db.session.rollback()
        flash('删除应用时发生错误', 'danger')
        logger.error(f"Error deleting application: {str(e)}")

    return redirect(url_for('main.list_applications'))

@main_bp.route('/applications/<int:id>/regenerate_key', methods=['POST'])
@login_required
def regenerate_api_key(id):
    """重新生成API密钥"""
    application = Application.query.get_or_404(id)

    try:
        application.api_key = security_manager.generate_api_key()
        db.session.commit()

        flash('API密钥重新生成成功', 'success')
        logger.info(f"API key regenerated for application: {application.name}")

    except Exception as e:
        db.session.rollback()
        flash('重新生成API密钥时发生错误', 'danger')
        logger.error(f"Error regenerating API key: {str(e)}")

    return redirect(url_for('main.list_applications'))

@main_bp.route('/applications/<int:id>/manage_models')
@login_required
def manage_app_models(id):
    """管理应用模型关联"""
    application = Application.query.get_or_404(id)
    app_models = AppModel.query.filter_by(application_id=id).all()
    available_models = AIModel.query.filter(
        ~AIModel.id.in_([am.model_id for am in app_models])
    ).all()

    return render_template('applications/manage_models.html',
                         application=application,
                         app_models=app_models,
                         available_models=available_models)

# ======== 应用模型关联管理 ========
@main_bp.route('/app_models')
@login_required
def list_app_models():
    """应用模型关联列表"""
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)

    app_models = AppModel.query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    return render_template('app_models/list.html', app_models=app_models)

@main_bp.route('/app_models/add', methods=['GET', 'POST'])
@login_required
def add_app_model():
    """添加应用模型关联"""
    form = AppModelForm()

    # 设置选择项
    applications = Application.query.all()
    models = AIModel.query.all()
    form.application_id.choices = [(a.id, a.name) for a in applications]
    form.model_id.choices = [(m.id, m.display_name) for m in models]

    if form.validate_on_submit():
        try:
            # 检查关联是否已存在
            existing = AppModel.query.filter_by(
                application_id=form.application_id.data,
                model_id=form.model_id.data
            ).first()

            if existing:
                flash('该应用模型关联已存在', 'danger')
                return render_template('app_models/add.html', form=form)

            app_model = AppModel(
                application_id=form.application_id.data,
                model_id=form.model_id.data,
                is_default=form.is_default.data
            )

            db.session.add(app_model)
            db.session.commit()

            flash('应用模型关联添加成功', 'success')
            logger.info(f"App model association added: App {app_model.application.name} - Model {app_model.model.display_name}")
            return redirect(url_for('main.list_app_models'))

        except Exception as e:
            db.session.rollback()
            flash('添加应用模型关联时发生错误', 'danger')
            logger.error(f"Error adding app model association: {str(e)}")

    return render_template('app_models/add.html', form=form)
