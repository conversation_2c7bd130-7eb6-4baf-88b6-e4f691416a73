import hashlib
import secrets
import time
from functools import wraps
from flask import session, request, jsonify, flash, redirect, url_for, current_app
import re

class SecurityManager:
    """安全管理器"""
    
    def __init__(self):
        self.login_attempts = {}  # 存储登录尝试次数
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', 
                                          password.encode('utf-8'), 
                                          salt.encode('utf-8'), 
                                          100000)
        return f"{salt}:{password_hash.hex()}"
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """验证密码"""
        try:
            salt, password_hash = hashed.split(':')
            return hashlib.pbkdf2_hmac('sha256',
                                     password.encode('utf-8'),
                                     salt.encode('utf-8'),
                                     100000).hex() == password_hash
        except ValueError:
            return False
    
    def generate_api_key(self) -> str:
        """生成API密钥"""
        return f"sk-{secrets.token_urlsafe(32)}"
    
    def check_login_attempts(self, identifier: str) -> bool:
        """检查登录尝试次数"""
        current_time = time.time()
        max_attempts = current_app.config.get('MAX_LOGIN_ATTEMPTS', 5)
        timeout = current_app.config.get('LOGIN_ATTEMPT_TIMEOUT', 300)
        
        if identifier in self.login_attempts:
            attempts, last_attempt = self.login_attempts[identifier]
            
            # 如果超时，重置计数
            if current_time - last_attempt > timeout:
                del self.login_attempts[identifier]
                return True
            
            # 如果超过最大尝试次数
            if attempts >= max_attempts:
                return False
        
        return True
    
    def record_login_attempt(self, identifier: str, success: bool):
        """记录登录尝试"""
        current_time = time.time()
        
        if success:
            # 成功登录，清除记录
            if identifier in self.login_attempts:
                del self.login_attempts[identifier]
        else:
            # 失败登录，增加计数
            if identifier in self.login_attempts:
                attempts, _ = self.login_attempts[identifier]
                self.login_attempts[identifier] = (attempts + 1, current_time)
            else:
                self.login_attempts[identifier] = (1, current_time)
    
    def validate_input(self, input_str: str, input_type: str = 'general') -> bool:
        """输入验证"""
        if not input_str:
            return False
        
        # 基本长度检查
        if len(input_str) > 1000:
            return False
        
        # SQL注入检查
        sql_patterns = [
            r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)',
            r'(--|#|/\*|\*/)',
            r'(\bOR\b.*=.*\bOR\b)',
            r'(\bAND\b.*=.*\bAND\b)',
            r'(\'.*\'|".*")',
        ]
        
        for pattern in sql_patterns:
            if re.search(pattern, input_str, re.IGNORECASE):
                return False
        
        # 特定类型验证
        if input_type == 'name':
            return re.match(r'^[a-zA-Z0-9_\-\u4e00-\u9fff\s]{1,100}$', input_str) is not None
        elif input_type == 'url':
            return re.match(r'^https?://[^\s/$.?#].[^\s]*$', input_str) is not None
        elif input_type == 'api_key':
            return re.match(r'^[a-zA-Z0-9_\-]{10,100}$', input_str) is not None
        
        return True

# 全局安全管理器实例
security_manager = SecurityManager()

def login_required(f):
    """登录检查装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            flash('请先登录', 'danger')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

def api_enabled_required(f):
    """API开关检查装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_app.config.get('API_ENABLED', False):
            return jsonify({"error": "API功能已禁用"}), 403
        return f(*args, **kwargs)
    return decorated_function

def validate_api_key(f):
    """API密钥验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key') or request.args.get('api_key')
        if not api_key:
            return jsonify({"error": "缺少API密钥"}), 401
        
        # 这里应该从数据库验证API密钥
        # 暂时使用简单验证
        if not security_manager.validate_input(api_key, 'api_key'):
            return jsonify({"error": "无效的API密钥格式"}), 401
        
        return f(*args, **kwargs)
    return decorated_function

def rate_limit(max_requests: int = 100, window: int = 3600):
    """简单的速率限制装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 这里可以实现更复杂的速率限制逻辑
            # 暂时只是一个占位符
            return f(*args, **kwargs)
        return decorated_function
    return decorator
