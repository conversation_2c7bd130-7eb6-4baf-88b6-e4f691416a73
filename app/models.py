# models.py
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from decimal import Decimal

db = SQLAlchemy()

class Platform(db.Model):
    __tablename__ = 'platforms'
    __table_args__ = (
        db.Index('idx_platform_name', 'name'),
    )

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False, index=True)
    base_url = db.Column(db.String(255), nullable=False)
    api_key = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    models = db.relationship("AIModel", back_populates="platform", cascade="all, delete-orphan")

    def to_dict(self, include_sensitive=False):
        data = {
            'id': self.id,
            'name': self.name,
            'base_url': self.base_url,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'models_count': len(self.models)
        }
        if include_sensitive:
            data['api_key'] = self.api_key
        return data

    def __repr__(self):
        return f'<Platform {self.name}>'

class AIModel(db.Model):
    __tablename__ = 'ai_models'
    __table_args__ = (
        db.Index('idx_model_display_name', 'display_name'),
        db.Index('idx_model_platform', 'platform_id'),
        db.Index('idx_model_visible', 'is_visible_model'),
        db.Index('idx_model_free', 'free'),
    )

    id = db.Column(db.Integer, primary_key=True)
    display_name = db.Column(db.String(50), unique=True, nullable=False, index=True)
    internal_name = db.Column(db.String(100), nullable=False)
    api_endpoint = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 价格字段 (每1000个token的价格，单位为美元)
    input_token_price = db.Column(db.Numeric(10, 6), default=0.000000)  # 输入token价格
    output_token_price = db.Column(db.Numeric(10, 6), default=0.000000)  # 输出token价格
    input_picture_price = db.Column(db.Numeric(10, 6), default=0.000000)  # 输入图片价格
    is_visible_model = db.Column(db.Boolean, default=False, index=True)
    free = db.Column(db.Boolean, default=False, index=True)  # 是否免费
    high_price = db.Column(db.Boolean, default=False)  # 是否高价

    platform_id = db.Column(db.Integer, db.ForeignKey('platforms.id'), nullable=False, index=True)
    platform = db.relationship("Platform", back_populates="models")

    # 新增与应用关系
    app_models = db.relationship("AppModel", back_populates="model", cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'display_name': self.display_name,
            'platform': self.platform.name if self.platform else None,
            'platform_id': self.platform_id,
            'internal_name': self.internal_name,
            'api_endpoint': self.api_endpoint,
            'is_visible_model': self.is_visible_model,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'input_token_price': float(self.input_token_price),
            'output_token_price': float(self.output_token_price),
            'input_picture_price': float(self.input_picture_price),
            'free': self.free,
            'high_price': self.high_price,
            'app_models_count': len(self.app_models)
        }

    def __repr__(self):
        return f'<AIModel {self.display_name}>'

class Application(db.Model):
    __tablename__ = 'applications'
    __table_args__ = (
        db.Index('idx_app_name', 'name'),
        db.Index('idx_app_api_key', 'api_key'),
    )

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False, index=True)
    description = db.Column(db.Text)
    api_key = db.Column(db.String(255), unique=True, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True, index=True)

    # 应用与模型的关系
    app_models = db.relationship("AppModel", back_populates="application", cascade="all, delete-orphan")

    def to_dict(self, include_sensitive=False):
        data = {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_active': self.is_active,
            'models_count': len(self.app_models)
        }
        if include_sensitive:
            data['api_key'] = self.api_key
        return data

    def __repr__(self):
        return f'<Application {self.name}>'

class AppModel(db.Model):
    __tablename__ = 'app_models'
    __table_args__ = (
        db.UniqueConstraint('application_id', 'model_id', name='uix_app_model'),
        db.Index('idx_app_model_app', 'application_id'),
        db.Index('idx_app_model_model', 'model_id'),
        db.Index('idx_app_model_default', 'is_default'),
    )

    id = db.Column(db.Integer, primary_key=True)
    application_id = db.Column(db.Integer, db.ForeignKey('applications.id'), nullable=False, index=True)
    model_id = db.Column(db.Integer, db.ForeignKey('ai_models.id'), nullable=False, index=True)
    is_default = db.Column(db.Boolean, default=False, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    application = db.relationship("Application", back_populates="app_models")
    model = db.relationship("AIModel", back_populates="app_models")

    def to_dict(self):
        return {
            'id': self.id,
            'application_id': self.application_id,
            'application_name': self.application.name if self.application else None,
            'model_id': self.model_id,
            'model_name': self.model.display_name if self.model else None,
            'is_default': self.is_default,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def __repr__(self):
        return f'<AppModel {self.application.name if self.application else "Unknown"} - {self.model.display_name if self.model else "Unknown"}>'
