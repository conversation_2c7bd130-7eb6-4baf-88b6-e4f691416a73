services:
  model-registry-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: model-registry-dev
    restart: unless-stopped
    ports:
      - "${FLASK_RUN_PORT:-5000}:5000"
    env_file:
      - .env.development  # 开发环境配置文件
    environment:
      FLASK_ENV: development
      FLASK_DEBUG: "True"
      PYTHONUNBUFFERED: 1
    volumes:
      - ./app:/app  # 开发环境挂载app目录以支持热重载
      - dev_logs:/app/logs
      - dev_uploads:/app/uploads
    networks:
      - model-registry-dev-network
    depends_on:
      mysql-dev:
        condition: service_healthy
    working_dir: /app
    command: ["python", "app.py"]

  mysql-dev:
    image: mysql:8.0
    container_name: mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: model_registry_dev
      MYSQL_USER: app_user
      MYSQL_PASSWORD: app_password
    ports:
      - "${DB_DEV_PORT:-3307}:3306"  # 使用不同端口避免冲突
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - model-registry-dev-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    command: --default-authentication-plugin=mysql_native_password

networks:
  model-registry-dev-network:
    driver: bridge

volumes:
  mysql_dev_data:
    driver: local
  dev_logs:
    driver: local
  dev_uploads:
    driver: local
