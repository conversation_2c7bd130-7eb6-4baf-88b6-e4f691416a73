# Docker Compose V2 部署指南

本项目已适配 Docker Compose V2，提供了生产环境和开发环境的完整部署方案。

## 前置要求

- Docker Engine 20.10.0+
- Docker Compose V2 (通过 `docker compose` 命令)

检查版本：
```bash
docker --version
docker compose version
```

## 快速开始

### 生产环境部署

1. **准备环境配置**
```bash
# 复制生产环境配置模板
cp .env.production .env

# 编辑配置文件，修改敏感信息
nano .env
```

2. **启动服务**
```bash
# 构建并启动所有服务
docker compose up -d

# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f model-registry
```

3. **访问应用**
- Web界面: http://localhost:5000
- API文档: http://localhost:5000/api/v1/health

### 开发环境部署

1. **准备开发环境配置**
```bash
# 复制开发环境配置
cp .env.development .env.dev
```

2. **启动开发环境**
```bash
# 使用开发环境配置启动
docker compose -f docker-compose.dev.yml up -d

# 查看开发环境日志
docker compose -f docker-compose.dev.yml logs -f
```

## 服务架构

### 生产环境服务
- `model-registry`: 主应用服务
- `db`: MySQL 8.0 数据库

### 开发环境服务
- `model-registry-dev`: 开发版应用（支持热重载）
- `mysql-dev`: 开发用 MySQL 数据库

## 环境变量配置

### 主要配置项
```bash
# 应用配置
FLASK_RUN_PORT=5000          # 应用端口
FLASK_ENV=production         # 环境类型

# 数据库配置
DB_ROOT_PASSWORD=rootpassword # 数据库root密码
DB_NAME=model_registry       # 数据库名
DB_USER=app_user            # 应用数据库用户
DB_PASSWORD=app_password    # 应用数据库密码
DB_PORT=3306               # 数据库端口

# 开发环境特定
DB_DEV_PORT=3307           # 开发环境数据库端口
```

## Docker Compose V2 新特性

### 1. 移除版本声明
```yaml
# V1 (旧版本)
version: '3.8'
services: ...

# V2 (新版本)
services: ...
```

### 2. 改进的环境变量语法
```yaml
# V2 推荐语法
environment:
  FLASK_ENV: production
  PYTHONUNBUFFERED: 1

# 而不是
environment:
  - FLASK_ENV=production
  - PYTHONUNBUFFERED=1
```

### 3. 增强的依赖管理
```yaml
depends_on:
  mysql-dev:
    condition: service_healthy  # 等待服务健康检查通过
```

### 4. 命名卷管理
```yaml
volumes:
  db_data:
    driver: local
  logs:
    driver: local
```

## 常用命令

### 基本操作
```bash
# 启动服务
docker compose up -d

# 停止服务
docker compose down

# 重启服务
docker compose restart

# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f [service_name]
```

### 开发环境操作
```bash
# 启动开发环境
docker compose -f docker-compose.dev.yml up -d

# 停止开发环境
docker compose -f docker-compose.dev.yml down

# 重建开发环境
docker compose -f docker-compose.dev.yml up --build -d
```

### 数据库操作
```bash
# 连接到生产数据库
docker compose exec db mysql -u root -p

# 连接到开发数据库
docker compose -f docker-compose.dev.yml exec mysql-dev mysql -u root -p

# 备份数据库
docker compose exec db mysqldump -u root -p model_registry > backup.sql

# 恢复数据库
docker compose exec -T db mysql -u root -p model_registry < backup.sql
```

### 应用管理
```bash
# 查看应用日志
docker compose logs -f model-registry

# 进入应用容器
docker compose exec model-registry bash

# 重启应用服务
docker compose restart model-registry

# 更新应用
docker compose pull
docker compose up -d
```

## 数据持久化

### 生产环境卷
- `db_data`: 数据库数据
- `logs`: 应用日志
- `uploads`: 上传文件

### 开发环境卷
- `mysql_dev_data`: 开发数据库数据
- `dev_logs`: 开发环境日志
- `dev_uploads`: 开发环境上传文件

### 卷管理命令
```bash
# 查看所有卷
docker volume ls

# 查看卷详情
docker volume inspect <volume_name>

# 清理未使用的卷
docker volume prune
```

## 健康检查

### 应用健康检查
```bash
# 检查应用健康状态
curl -f http://localhost:5000/api/v1/health

# 查看健康检查日志
docker compose logs model-registry | grep health
```

### 数据库健康检查
```bash
# 检查数据库状态
docker compose exec db mysqladmin ping -h localhost
```

## 故障排除

### 常见问题

1. **端口冲突**
```bash
# 修改端口配置
export FLASK_RUN_PORT=5001
docker compose up -d
```

2. **数据库连接失败**
```bash
# 检查数据库状态
docker compose logs db

# 重启数据库
docker compose restart db
```

3. **权限问题**
```bash
# 修复卷权限
docker compose exec model-registry chown -R app:app /app/logs
```

4. **清理和重建**
```bash
# 完全清理并重建
docker compose down -v
docker compose build --no-cache
docker compose up -d
```

## 监控和日志

### 日志查看
```bash
# 实时查看所有服务日志
docker compose logs -f

# 查看特定服务日志
docker compose logs -f model-registry

# 查看最近的日志
docker compose logs --tail=100 model-registry
```

### 资源监控
```bash
# 查看容器资源使用
docker stats

# 查看特定容器资源
docker stats model-registry
```

## 安全建议

1. **生产环境**
   - 使用强密码
   - 限制数据库端口暴露
   - 定期更新镜像
   - 使用 secrets 管理敏感信息

2. **网络安全**
   - 使用自定义网络
   - 限制容器间通信
   - 配置防火墙规则

3. **数据安全**
   - 定期备份数据
   - 加密敏感数据
   - 监控访问日志
